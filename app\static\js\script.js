document.addEventListener('DOMContentLoaded', () => {
    // Get DOM elements
    const enhanceModeBtn = document.getElementById('enhance-mode-btn');
    const writeModeBtn = document.getElementById('write-mode-btn');
    const enhanceForm = document.getElementById('email-form');
    const writeForm = document.getElementById('write-form');
    const emailInput = document.getElementById('email-input');
    const promptInput = document.getElementById('prompt-input');
    const enhancementType = document.getElementById('enhancement-type');
    const loader = document.getElementById('loader');
    const loaderText = document.getElementById('loader-text');
    const errorMessage = document.getElementById('error-message');
    const resultSection = document.getElementById('result-section');
    const resultTitle = document.getElementById('result-title');
    const resultOutput = document.getElementById('result-output');
    const copyBtn = document.getElementById('copy-btn');
    const downloadBtn = document.getElementById('download-btn');

    let currentMode = 'enhance';

    // Mode switching functionality
    enhanceModeBtn.addEventListener('click', () => switchMode('enhance'));
    writeModeBtn.addEventListener('click', () => switchMode('write'));

    function switchMode(mode) {
        currentMode = mode;

        // Update button states
        enhanceModeBtn.classList.toggle('active', mode === 'enhance');
        writeModeBtn.classList.toggle('active', mode === 'write');

        // Show/hide forms
        enhanceForm.hidden = mode !== 'enhance';
        writeForm.hidden = mode !== 'write';

        // Hide results when switching modes
        resultSection.hidden = true;
        errorMessage.hidden = true;
    }

    // Enhancement form submission handler
    enhanceForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        // Reset previous states
        showLoader('Enhancing your email...');
        hideResults();

        try {
            const result = await enhanceEmail(emailInput.value, enhancementType.value);
            showResult('Enhanced Email', result);
        } catch (error) {
            showError(error.message || 'Failed to enhance email');
        }
    });

    // Writing form submission handler
    writeForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        // Reset previous states
        showLoader('Writing your email...');
        hideResults();

        try {
            const result = await writeEmail(promptInput.value);
            showResult('Generated Email', result);
        } catch (error) {
            showError(error.message || 'Failed to write email');
        }
    });

    // Helper functions
    function showLoader(text) {
        loaderText.textContent = text;
        loader.hidden = false;
        errorMessage.hidden = true;
        resultSection.hidden = true;
    }

    function hideResults() {
        errorMessage.hidden = true;
        resultSection.hidden = true;
    }

    function showResult(title, content) {
        loader.hidden = true;
        resultTitle.textContent = title;
        resultOutput.textContent = content;
        resultSection.hidden = false;
    }

    function showError(message) {
        loader.hidden = true;
        errorMessage.textContent = message;
        errorMessage.hidden = false;
    }

    // Copy to clipboard functionality
    copyBtn.addEventListener('click', () => {
        navigator.clipboard.writeText(resultOutput.textContent)
            .then(() => {
                const originalText = copyBtn.querySelector('.btn-text').textContent;
                copyBtn.querySelector('.btn-text').textContent = 'Copied!';
                setTimeout(() => {
                    copyBtn.querySelector('.btn-text').textContent = originalText;
                }, 2000);
            })
            .catch(err => {
                console.error('Copy failed', err);
                alert('Failed to copy content');
            });
    });

    // Download functionality
    downloadBtn.addEventListener('click', () => {
        const blob = new Blob([resultOutput.textContent], { type: 'text/plain' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        const filename = currentMode === 'enhance' ? 'enhanced-email.txt' : 'generated-email.txt';
        link.download = filename;
        link.click();
    });

    // API call for email enhancement
    async function enhanceEmail(emailText, enhancementType) {
        const response = await fetch('/enhance_email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email_text: emailText,
                enhancement_type: enhancementType
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to enhance email');
        }

        const data = await response.json();
        return data.enhanced_text;
    }

    // API call for email writing
    async function writeEmail(promptText) {
        const response = await fetch('/write_email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                prompt_text: promptText
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to write email');
        }

        const data = await response.json();
        return data.generated_email;
    }
});