# AI Email Enhancer

A Flask-based web application that uses OpenAI's GPT models to enhance and write professional emails.

## Features

- **Email Enhancement**: Improve existing emails with different styles (professional, friendly, concise, detailed)
- **Email Writing**: Generate new emails from prompts
- **Web Interface**: User-friendly web interface for easy interaction
- **REST API**: RESTful endpoints for programmatic access

## Setup Instructions

### Prerequisites

- Python 3.8 or higher
- OpenAI API key

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/nams2002/AI-Email-Enhancer.git
   cd AI-Email-Enhancer
   ```

2. **Create a virtual environment**
   ```bash
   python -m venv myenv
   
   # On Windows
   myenv\Scripts\activate
   
   # On macOS/Linux
   source myenv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   # Copy the example environment file
   cp .env.example .env
   
   # Edit .env and add your OpenAI API key
   # OPENAI_API_KEY=your_actual_api_key_here
   ```

5. **Run the application**
   ```bash
   python run.py
   ```

The application will be available at `http://localhost:5000`

## API Endpoints

### Enhance Email
- **URL**: `/enhance_email`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Body**:
  ```json
  {
    "email_text": "Your email content here",
    "enhancement_type": "professional"
  }
  ```
- **Enhancement Types**: `professional`, `friendly`, `concise`, `detailed`

### Write Email
- **URL**: `/write_email`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Body**:
  ```json
  {
    "prompt_text": "Description of the email you want to write"
  }
  ```

## Project Structure

```
ai_email_enhancer/
├── app/
│   ├── __init__.py
│   ├── main.py          # Flask app factory
│   ├── routes.py        # API routes
│   ├── ai_enhancer.py   # OpenAI integration
│   ├── static/          # CSS, JS, images
│   └── templates/       # HTML templates
├── config.py            # Configuration settings
├── run.py              # Application entry point
├── requirements.txt    # Python dependencies
├── .env.example       # Environment variables template
└── README.md          # This file
```

## Security Notes

- Never commit your `.env` file or API keys to version control
- The `.gitignore` file is configured to exclude sensitive files
- Use environment variables for all sensitive configuration

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.
