from app.main import create_app
import os

def main():
    # Check if OpenAI API key is set in environment
    if 'OPENAI_API_KEY' not in os.environ:
        print("Warning: OPENAI_API_KEY environment variable not set.")
        print("Please set your OpenAI API key as an environment variable:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        print("Or create a .env file with OPENAI_API_KEY=your-api-key-here")
        return

    # Create and configure the Flask app
    app = create_app()
    
    # Run the application
    # In production, use a production WSGI server like Gunicorn
    app.run(
        host='0.0.0.0',  # Listen on all available network interfaces
        port=5000,        # Default port, can be changed
        debug=True     # Set to False in production
    )

if __name__ == '__main__':
    main()