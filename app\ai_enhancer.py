import os
from openai import OpenAI

def enhance_email(email_text, enhancement_type='professional'):
    """
    Enhance the given email text using OpenAI's AI

    Args:
        email_text (str): The original email text to be enhanced
        enhancement_type (str): Type of enhancement (professional, friendly, concise, detailed)

    Returns:
        str: The enhanced email text
    """
    try:
        # Initialize OpenAI client with minimal configuration
        api_key = os.environ.get('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OpenAI API key not found")

        client = OpenAI(api_key=api_key)

        # Create enhancement-specific instructions
        enhancement_instructions = {
            'professional': "Make the email more professional, formal, and business-appropriate while maintaining clarity.",
            'friendly': "Make the email warmer, more approachable, and friendly while keeping it professional.",
            'concise': "Make the email more concise and to-the-point while preserving all important information.",
            'detailed': "Expand the email with more detail and context while maintaining clarity and structure."
        }

        instruction = enhancement_instructions.get(enhancement_type, enhancement_instructions['professional'])

        # Create the prompt for email enhancement
        prompt = f"""You are an expert email editor. Please enhance the following email with this focus: {instruction}

        Additional guidelines:
        - Improve clarity and readability
        - Correct grammar and spelling
        - Ensure the core message remains intact
        - Use appropriate email structure

        Original Email:
        {email_text}

        Enhanced Email:
        """

        # Generate the enhanced email
        response = client.chat.completions.create(
            model="gpt-4",
            max_tokens=1000,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        )

        # Extract and return the enhanced text
        return response.choices[0].message.content.strip()
    
    except Exception as e:
        # Handle any errors in AI enhancement
        return f"Error enhancing email: {str(e)}"

def write_email(prompt_text):
    """
    Write a new email based on the given prompt using OpenAI's AI

    Args:
        prompt_text (str): The prompt describing what email to write

    Returns:
        str: The generated email text
    """
    try:
        # Initialize OpenAI client with minimal configuration
        api_key = os.environ.get('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OpenAI API key not found")

        client = OpenAI(api_key=api_key)

        # Create the prompt for email writing
        system_prompt = """You are an expert email writer. Write professional, clear, and well-structured emails based on the user's requirements.

        Guidelines:
        - Use appropriate greeting and closing
        - Maintain professional tone unless specified otherwise
        - Be clear and concise
        - Include proper email structure (subject line if requested)
        - Adapt tone based on context (formal, casual, business, etc.)
        """

        user_prompt = f"""Please write an email based on this request:

        {prompt_text}

        Email:
        """

        # Generate the email
        response = client.chat.completions.create(
            model="gpt-4",
            max_tokens=1000,
            messages=[
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": user_prompt
                }
            ]
        )

        # Extract and return the generated email
        return response.choices[0].message.content.strip()

    except Exception as e:
        # Handle any errors in email writing
        return f"Error writing email: {str(e)}"