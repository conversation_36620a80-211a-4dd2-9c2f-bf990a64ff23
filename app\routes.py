import logging
from flask import Blueprint, render_template, request, jsonify, current_app
from werkzeug.exceptions import BadRequest, MethodNotAllowed
from .ai_enhancer import enhance_email, write_email

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Create a blueprint for routes
main_blueprint = Blueprint('main', __name__)

@main_blueprint.route('/')
def index():
    """
    Render the main index page
    """
    return render_template('base.html')

@main_blueprint.route('/enhance_email', methods=['POST'])
def enhance_email_route():
    """
    Route to handle email enhancement requests with comprehensive error handling
    """
    # Validate request content type
    if not request.is_json:
        logger.warning('Invalid request: Content-Type must be application/json')
        return jsonify({
            'error': 'Invalid request. Content-Type must be application/json',
            'status': 'error'
        }), 415  # Unsupported Media Type
    
    try:
        # Get request data
        data = request.get_json()
        
        # Extract email text with validation
        email_text = data.get('email_text', '').strip()
        enhancement_type = data.get('enhancement_type', 'professional')
        
        # Comprehensive input validation
        if not email_text:
            logger.warning('No email text provided in enhancement request')
            return jsonify({
                'error': 'Email text is required',
                'status': 'error',
                'details': 'Please provide the email text you want to enhance'
            }), 400
        
        # Validate enhancement type
        valid_types = ['professional', 'friendly', 'concise', 'detailed']
        if enhancement_type not in valid_types:
            logger.warning(f'Invalid enhancement type: {enhancement_type}')
            return jsonify({
                'error': 'Invalid enhancement type',
                'status': 'error',
                'valid_types': valid_types
            }), 400
        
        # Enhance the email
        try:
            enhanced_text = enhance_email(
                email_text, 
                enhancement_type=enhancement_type
            )
        except Exception as enhance_error:
            logger.error(f'Email enhancement failed: {str(enhance_error)}')
            return jsonify({
                'error': 'Failed to enhance email',
                'status': 'error',
                'details': str(enhance_error)
            }), 500
        
        # Log successful enhancement
        logger.info(f'Email enhanced successfully (type: {enhancement_type})')
        
        # Return the enhanced email
        return jsonify({
            'enhanced_text': enhanced_text,
            'status': 'success',
            'enhancement_type': enhancement_type
        })
    
    except BadRequest as e:
        # Handle bad request errors
        logger.warning(f'Bad request error: {str(e)}')
        return jsonify({
            'error': 'Invalid request',
            'status': 'error',
            'details': str(e)
        }), 400
    
    except Exception as e:
        # Catch-all for unexpected errors
        logger.error(f'Unexpected error in email enhancement: {str(e)}')
        return jsonify({
            'error': 'An unexpected error occurred',
            'status': 'error'
        }), 500

@main_blueprint.route('/write_email', methods=['POST'])
def write_email_route():
    """
    Route to handle email writing requests from prompts
    """
    # Validate request content type
    if not request.is_json:
        logger.warning('Invalid request: Content-Type must be application/json')
        return jsonify({
            'error': 'Invalid request. Content-Type must be application/json',
            'status': 'error'
        }), 415

    try:
        # Get request data
        data = request.get_json()

        # Extract prompt text with validation
        prompt_text = data.get('prompt_text', '').strip()

        # Input validation
        if not prompt_text:
            logger.warning('No prompt text provided in write email request')
            return jsonify({
                'error': 'Prompt text is required',
                'status': 'error',
                'details': 'Please provide a description of the email you want to write'
            }), 400

        # Write the email
        try:
            generated_email = write_email(prompt_text)
        except Exception as write_error:
            logger.error(f'Email writing failed: {str(write_error)}')
            return jsonify({
                'error': 'Failed to write email',
                'status': 'error',
                'details': str(write_error)
            }), 500

        # Log successful email writing
        logger.info('Email written successfully from prompt')

        # Return the generated email
        return jsonify({
            'generated_email': generated_email,
            'status': 'success'
        })

    except BadRequest as e:
        # Handle bad request errors
        logger.warning(f'Bad request error: {str(e)}')
        return jsonify({
            'error': 'Invalid request',
            'status': 'error',
            'details': str(e)
        }), 400

    except Exception as e:
        # Catch-all for unexpected errors
        logger.error(f'Unexpected error in email writing: {str(e)}')
        return jsonify({
            'error': 'An unexpected error occurred',
            'status': 'error'
        }), 500

# Custom error handlers
@main_blueprint.app_errorhandler(404)
def page_not_found(e):
    """
    Custom 404 error handler
    """
    logger.warning(f'Page not found: {request.url}')
    return render_template('404.html'), 404

@main_blueprint.app_errorhandler(500)
def internal_server_error(e):
    """
    Custom 500 error handler
    """
    logger.error(f'Internal server error: {str(e)}')
    return render_template('500.html'), 500